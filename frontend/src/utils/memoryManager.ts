/**
 * 内存管理工具类
 * 用于监控和清理应用中的内存泄漏
 */

interface MemoryStats {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
  timestamp: number;
}

interface CleanupFunction {
  name: string;
  cleanup: () => void;
  priority: number; // 1-10, 10为最高优先级
}

class MemoryManager {
  private static instance: MemoryManager;
  private cleanupFunctions: CleanupFunction[] = [];
  private memoryStats: MemoryStats[] = [];
  private monitoringInterval: number | null = null;
  private isMonitoring = false;
  private memoryThreshold = 0.8; // 80%内存使用率阈值
  private maxStatsHistory = 100; // 最多保存100条历史记录

  static getInstance(): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager();
    }
    return MemoryManager.instance;
  }

  /**
   * 注册清理函数
   */
  registerCleanup(name: string, cleanup: () => void, priority: number = 5): void {
    // 移除已存在的同名清理函数
    this.cleanupFunctions = this.cleanupFunctions.filter(fn => fn.name !== name);
    
    // 添加新的清理函数
    this.cleanupFunctions.push({ name, cleanup, priority });
    
    // 按优先级排序
    this.cleanupFunctions.sort((a, b) => b.priority - a.priority);
    
    console.log(`[MemoryManager] 注册清理函数: ${name}, 优先级: ${priority}`);
  }

  /**
   * 注销清理函数
   */
  unregisterCleanup(name: string): void {
    this.cleanupFunctions = this.cleanupFunctions.filter(fn => fn.name !== name);
    console.log(`[MemoryManager] 注销清理函数: ${name}`);
  }

  /**
   * 执行所有清理函数
   */
  executeCleanup(force: boolean = false): void {
    console.log(`[MemoryManager] 开始执行清理，强制模式: ${force}`);
    
    let cleanedCount = 0;
    this.cleanupFunctions.forEach(({ name, cleanup, priority }) => {
      try {
        console.log(`[MemoryManager] 执行清理: ${name} (优先级: ${priority})`);
        cleanup();
        cleanedCount++;
      } catch (error) {
        console.error(`[MemoryManager] 清理函数 ${name} 执行失败:`, error);
      }
    });

    console.log(`[MemoryManager] 清理完成，执行了 ${cleanedCount} 个清理函数`);

    // 强制垃圾回收（如果可用）
    if (force && window.gc) {
      try {
        window.gc();
        console.log(`[MemoryManager] 强制垃圾回收完成`);
      } catch (error) {
        console.warn(`[MemoryManager] 强制垃圾回收失败:`, error);
      }
    }
  }

  /**
   * 获取当前内存使用情况
   */
  getCurrentMemoryStats(): MemoryStats | null {
    if (!('memory' in performance)) {
      console.warn('[MemoryManager] 浏览器不支持 performance.memory API');
      return null;
    }

    const memory = (performance as any).memory;
    return {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      timestamp: Date.now()
    };
  }

  /**
   * 开始内存监控
   */
  startMonitoring(interval: number = 30000): void { // 默认30秒
    if (this.isMonitoring) {
      console.warn('[MemoryManager] 内存监控已在运行');
      return;
    }

    this.isMonitoring = true;
    this.monitoringInterval = window.setInterval(() => {
      const stats = this.getCurrentMemoryStats();
      if (!stats) return;

      // 保存统计信息
      this.memoryStats.push(stats);
      
      // 限制历史记录数量
      if (this.memoryStats.length > this.maxStatsHistory) {
        this.memoryStats.shift();
      }

      // 计算内存使用率
      const usageRatio = stats.usedJSHeapSize / stats.jsHeapSizeLimit;
      
      console.log(`[MemoryManager] 内存使用: ${(stats.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB / ${(stats.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB (${(usageRatio * 100).toFixed(1)}%)`);

      // 如果内存使用率超过阈值，执行清理
      if (usageRatio > this.memoryThreshold) {
        console.warn(`[MemoryManager] 内存使用率过高 (${(usageRatio * 100).toFixed(1)}%)，开始清理`);
        this.executeCleanup(true);
      }
    }, interval);

    console.log(`[MemoryManager] 开始内存监控，间隔: ${interval}ms`);
  }

  /**
   * 停止内存监控
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.isMonitoring = false;
    console.log('[MemoryManager] 停止内存监控');
  }

  /**
   * 获取内存使用历史
   */
  getMemoryHistory(): MemoryStats[] {
    return [...this.memoryStats];
  }

  /**
   * 设置内存阈值
   */
  setMemoryThreshold(threshold: number): void {
    if (threshold < 0 || threshold > 1) {
      throw new Error('内存阈值必须在 0-1 之间');
    }
    this.memoryThreshold = threshold;
    console.log(`[MemoryManager] 设置内存阈值: ${(threshold * 100).toFixed(1)}%`);
  }

  /**
   * 清理所有资源并销毁管理器
   */
  destroy(): void {
    this.stopMonitoring();
    this.executeCleanup(true);
    this.cleanupFunctions = [];
    this.memoryStats = [];
    console.log('[MemoryManager] 内存管理器已销毁');
  }
}

export default MemoryManager;

// 导出单例实例
export const memoryManager = MemoryManager.getInstance();

// 声明全局类型
declare global {
  interface Window {
    gc?: () => void;
  }
}
