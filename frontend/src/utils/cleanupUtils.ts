/**
 * 清理工具函数集合
 * 提供各种资源清理的通用方法
 */

import { memoryManager } from './memoryManager';

/**
 * 清理事件监听器
 */
export const cleanupEventListeners = (
  target: EventTarget,
  events: Array<{ type: string; listener: EventListener; options?: boolean | AddEventListenerOptions }>
): void => {
  events.forEach(({ type, listener, options }) => {
    try {
      target.removeEventListener(type, listener, options);
    } catch (error) {
      console.warn(`清理事件监听器失败 ${type}:`, error);
    }
  });
};

/**
 * 清理定时器
 */
export const cleanupTimers = (timers: Array<number | NodeJS.Timeout>): void => {
  timers.forEach(timer => {
    try {
      if (typeof timer === 'number') {
        clearTimeout(timer);
        clearInterval(timer);
      } else {
        clearTimeout(timer);
        clearInterval(timer);
      }
    } catch (error) {
      console.warn('清理定时器失败:', error);
    }
  });
};

/**
 * 清理动画帧
 */
export const cleanupAnimationFrames = (frameIds: number[]): void => {
  frameIds.forEach(frameId => {
    try {
      cancelAnimationFrame(frameId);
    } catch (error) {
      console.warn('清理动画帧失败:', error);
    }
  });
};

/**
 * 清理DOM元素
 */
export const cleanupDOMElements = (elements: HTMLElement[]): void => {
  elements.forEach(element => {
    try {
      // 移除所有事件监听器
      const clonedElement = element.cloneNode(true);
      element.parentNode?.replaceChild(clonedElement, element);
      
      // 从DOM中移除
      if (element.parentNode) {
        element.parentNode.removeChild(element);
      }
    } catch (error) {
      console.warn('清理DOM元素失败:', error);
    }
  });
};

/**
 * 清理音频元素
 */
export const cleanupAudioElement = (audioElement: HTMLAudioElement): void => {
  try {
    // 暂停播放
    audioElement.pause();

    // 移除所有事件监听器
    audioElement.onerror = null;
    audioElement.onloadedmetadata = null;
    audioElement.onloadstart = null;
    audioElement.onprogress = null;
    audioElement.oncanplay = null;
    audioElement.onended = null;
    audioElement.ontimeupdate = null;
    audioElement.onloadeddata = null;
    audioElement.oncanplaythrough = null;
    audioElement.ondurationchange = null;
    audioElement.onplay = null;
    audioElement.onpause = null;
    audioElement.onvolumechange = null;

    // 清空源
    audioElement.src = "";
    audioElement.srcObject = null;

    // 重新加载以确保停止所有音频处理
    audioElement.load();

    // 从DOM中移除
    if (audioElement.parentNode) {
      audioElement.parentNode.removeChild(audioElement);
    }
  } catch (error) {
    console.warn('清理音频元素失败:', error);
  }
};

/**
 * 清理视频元素
 */
export const cleanupVideoElement = (videoElement: HTMLVideoElement): void => {
  try {
    // 暂停播放
    videoElement.pause();

    // 移除所有事件监听器
    videoElement.onerror = null;
    videoElement.onloadedmetadata = null;
    videoElement.onloadstart = null;
    videoElement.onprogress = null;
    videoElement.oncanplay = null;
    videoElement.onended = null;
    videoElement.ontimeupdate = null;
    videoElement.onloadeddata = null;
    videoElement.oncanplaythrough = null;
    videoElement.ondurationchange = null;
    videoElement.onplay = null;
    videoElement.onpause = null;
    videoElement.onvolumechange = null;
    videoElement.onseeked = null;
    videoElement.onseeking = null;

    // 清空源
    videoElement.src = "";
    videoElement.srcObject = null;

    // 重新加载以确保停止所有视频处理
    videoElement.load();

    // 从DOM中移除
    if (videoElement.parentNode) {
      videoElement.parentNode.removeChild(videoElement);
    }
  } catch (error) {
    console.warn('清理视频元素失败:', error);
  }
};

/**
 * 清理Canvas对象
 */
export const cleanupCanvas = (canvas: any): void => {
  try {
    if (!canvas) return;

    // 移除所有对象
    const objects = canvas.getObjects ? canvas.getObjects() : [];
    objects.forEach((obj: any) => {
      try {
        // 移除事件监听器
        if (obj.off) {
          obj.off();
        }
        
        // 清理对象特定的资源
        if (obj.dispose) {
          obj.dispose();
        }
        
        // 从画布移除
        canvas.remove(obj);
      } catch (error) {
        console.warn('清理Canvas对象失败:', error);
      }
    });

    // 清理画布事件监听器
    if (canvas.off) {
      canvas.off();
    }

    // 清理画布
    if (canvas.clear) {
      canvas.clear();
    }

    // 销毁画布
    if (canvas.dispose) {
      canvas.dispose();
    }
  } catch (error) {
    console.warn('清理Canvas失败:', error);
  }
};

/**
 * 清理对象引用
 */
export const cleanupObjectReferences = (obj: any, propertiesToClear: string[]): void => {
  try {
    propertiesToClear.forEach(prop => {
      if (obj && obj.hasOwnProperty(prop)) {
        obj[prop] = null;
      }
    });
  } catch (error) {
    console.warn('清理对象引用失败:', error);
  }
};

/**
 * 清理缓存
 */
export const cleanupCache = (cache: Map<any, any> | Set<any> | any[]): void => {
  try {
    if (cache instanceof Map) {
      cache.clear();
    } else if (cache instanceof Set) {
      cache.clear();
    } else if (Array.isArray(cache)) {
      cache.length = 0;
    } else if (cache && typeof cache === 'object') {
      Object.keys(cache).forEach(key => {
        delete cache[key];
      });
    }
  } catch (error) {
    console.warn('清理缓存失败:', error);
  }
};

/**
 * 清理WebGL上下文
 */
export const cleanupWebGLContext = (gl: WebGLRenderingContext | WebGL2RenderingContext): void => {
  try {
    if (!gl) return;

    // 清理所有缓冲区
    const buffers = gl.getParameter(gl.ARRAY_BUFFER_BINDING);
    if (buffers) {
      gl.deleteBuffer(buffers);
    }

    // 清理所有纹理
    const textures = gl.getParameter(gl.TEXTURE_BINDING_2D);
    if (textures) {
      gl.deleteTexture(textures);
    }

    // 清理所有着色器程序
    const program = gl.getParameter(gl.CURRENT_PROGRAM);
    if (program) {
      gl.deleteProgram(program);
    }

    // 失去上下文
    const loseContext = gl.getExtension('WEBGL_lose_context');
    if (loseContext) {
      loseContext.loseContext();
    }
  } catch (error) {
    console.warn('清理WebGL上下文失败:', error);
  }
};

/**
 * 通用资源清理函数
 */
export const performGeneralCleanup = (): void => {
  console.log('[CleanupUtils] 开始执行通用清理');

  // 清理全局变量
  if (window.performance && window.performance.mark) {
    try {
      window.performance.clearMarks();
      window.performance.clearMeasures();
    } catch (error) {
      console.warn('清理性能标记失败:', error);
    }
  }

  // 清理控制台
  if (console.clear && process.env.NODE_ENV === 'production') {
    try {
      console.clear();
    } catch (error) {
      console.warn('清理控制台失败:', error);
    }
  }

  console.log('[CleanupUtils] 通用清理完成');
};

/**
 * 注册所有清理函数到内存管理器
 */
export const registerCleanupFunctions = (): void => {
  memoryManager.registerCleanup('general-cleanup', performGeneralCleanup, 1);
  
  console.log('[CleanupUtils] 已注册清理函数到内存管理器');
};
