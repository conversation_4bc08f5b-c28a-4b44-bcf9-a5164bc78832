/**
 * 内存泄漏测试工具
 * 用于测试和验证内存清理功能
 */

import { memoryManager } from './memoryManager';

interface TestResult {
  testName: string;
  beforeMemory: number;
  afterMemory: number;
  memoryDiff: number;
  success: boolean;
  details: string;
}

class MemoryTester {
  private testResults: TestResult[] = [];

  /**
   * 获取当前内存使用量
   */
  private getCurrentMemoryUsage(): number {
    const stats = memoryManager.getCurrentMemoryStats();
    return stats ? stats.usedJSHeapSize : 0;
  }

  /**
   * 强制垃圾回收
   */
  private forceGC(): void {
    if (window.gc) {
      window.gc();
    }
  }

  /**
   * 等待指定时间
   */
  private async wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 测试事件监听器泄漏
   */
  async testEventListenerLeak(): Promise<TestResult> {
    const testName = 'Event Listener Leak Test';
    console.log(`[MemoryTester] 开始 ${testName}`);

    this.forceGC();
    await this.wait(100);
    const beforeMemory = this.getCurrentMemoryUsage();

    // 创建大量事件监听器
    const listeners: Array<() => void> = [];
    const elements: HTMLElement[] = [];

    for (let i = 0; i < 1000; i++) {
      const element = document.createElement('div');
      const listener = () => console.log(`Event ${i}`);
      
      element.addEventListener('click', listener);
      listeners.push(listener);
      elements.push(element);
      document.body.appendChild(element);
    }

    await this.wait(100);

    // 清理事件监听器
    elements.forEach((element, index) => {
      element.removeEventListener('click', listeners[index]);
      document.body.removeChild(element);
    });

    this.forceGC();
    await this.wait(100);
    const afterMemory = this.getCurrentMemoryUsage();

    const memoryDiff = afterMemory - beforeMemory;
    const success = memoryDiff < 1024 * 1024; // 小于1MB认为成功

    const result: TestResult = {
      testName,
      beforeMemory,
      afterMemory,
      memoryDiff,
      success,
      details: `创建并清理了1000个事件监听器，内存差异: ${(memoryDiff / 1024).toFixed(2)}KB`
    };

    this.testResults.push(result);
    return result;
  }

  /**
   * 测试定时器泄漏
   */
  async testTimerLeak(): Promise<TestResult> {
    const testName = 'Timer Leak Test';
    console.log(`[MemoryTester] 开始 ${testName}`);

    this.forceGC();
    await this.wait(100);
    const beforeMemory = this.getCurrentMemoryUsage();

    // 创建大量定时器
    const timers: number[] = [];
    for (let i = 0; i < 1000; i++) {
      const timer = window.setTimeout(() => {
        // 空函数
      }, 10000); // 10秒后执行
      timers.push(timer);
    }

    await this.wait(100);

    // 清理定时器
    timers.forEach(timer => clearTimeout(timer));

    this.forceGC();
    await this.wait(100);
    const afterMemory = this.getCurrentMemoryUsage();

    const memoryDiff = afterMemory - beforeMemory;
    const success = memoryDiff < 512 * 1024; // 小于512KB认为成功

    const result: TestResult = {
      testName,
      beforeMemory,
      afterMemory,
      memoryDiff,
      success,
      details: `创建并清理了1000个定时器，内存差异: ${(memoryDiff / 1024).toFixed(2)}KB`
    };

    this.testResults.push(result);
    return result;
  }

  /**
   * 测试DOM元素泄漏
   */
  async testDOMElementLeak(): Promise<TestResult> {
    const testName = 'DOM Element Leak Test';
    console.log(`[MemoryTester] 开始 ${testName}`);

    this.forceGC();
    await this.wait(100);
    const beforeMemory = this.getCurrentMemoryUsage();

    // 创建大量DOM元素
    const elements: HTMLElement[] = [];
    for (let i = 0; i < 1000; i++) {
      const element = document.createElement('div');
      element.innerHTML = `<span>Element ${i}</span><img src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7">`;
      document.body.appendChild(element);
      elements.push(element);
    }

    await this.wait(100);

    // 清理DOM元素
    elements.forEach(element => {
      if (element.parentNode) {
        element.parentNode.removeChild(element);
      }
    });

    this.forceGC();
    await this.wait(100);
    const afterMemory = this.getCurrentMemoryUsage();

    const memoryDiff = afterMemory - beforeMemory;
    const success = memoryDiff < 2 * 1024 * 1024; // 小于2MB认为成功

    const result: TestResult = {
      testName,
      beforeMemory,
      afterMemory,
      memoryDiff,
      success,
      details: `创建并清理了1000个DOM元素，内存差异: ${(memoryDiff / 1024).toFixed(2)}KB`
    };

    this.testResults.push(result);
    return result;
  }

  /**
   * 测试内存管理器清理功能
   */
  async testMemoryManagerCleanup(): Promise<TestResult> {
    const testName = 'Memory Manager Cleanup Test';
    console.log(`[MemoryTester] 开始 ${testName}`);

    this.forceGC();
    await this.wait(100);
    const beforeMemory = this.getCurrentMemoryUsage();

    // 注册一些清理函数
    const cleanupData: any[] = [];
    for (let i = 0; i < 100; i++) {
      const data = new Array(1000).fill(`data-${i}`);
      cleanupData.push(data);
      
      memoryManager.registerCleanup(`test-cleanup-${i}`, () => {
        cleanupData[i] = null;
      }, 5);
    }

    await this.wait(100);

    // 执行清理
    memoryManager.executeCleanup(true);

    // 注销清理函数
    for (let i = 0; i < 100; i++) {
      memoryManager.unregisterCleanup(`test-cleanup-${i}`);
    }

    this.forceGC();
    await this.wait(100);
    const afterMemory = this.getCurrentMemoryUsage();

    const memoryDiff = afterMemory - beforeMemory;
    const success = memoryDiff < 1024 * 1024; // 小于1MB认为成功

    const result: TestResult = {
      testName,
      beforeMemory,
      afterMemory,
      memoryDiff,
      success,
      details: `测试内存管理器清理功能，内存差异: ${(memoryDiff / 1024).toFixed(2)}KB`
    };

    this.testResults.push(result);
    return result;
  }

  /**
   * 运行所有测试
   */
  async runAllTests(): Promise<TestResult[]> {
    console.log('[MemoryTester] 开始运行所有内存测试');
    
    this.testResults = [];

    await this.testEventListenerLeak();
    await this.testTimerLeak();
    await this.testDOMElementLeak();
    await this.testMemoryManagerCleanup();

    console.log('[MemoryTester] 所有测试完成');
    return this.testResults;
  }

  /**
   * 获取测试结果
   */
  getTestResults(): TestResult[] {
    return [...this.testResults];
  }

  /**
   * 打印测试报告
   */
  printReport(): void {
    console.log('\n=== 内存泄漏测试报告 ===');
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过测试: ${passedTests}`);
    console.log(`失败测试: ${totalTests - passedTests}`);
    console.log(`通过率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    console.log('\n详细结果:');
    this.testResults.forEach(result => {
      const status = result.success ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${result.testName}`);
      console.log(`  ${result.details}`);
      console.log(`  内存变化: ${(result.memoryDiff / 1024).toFixed(2)}KB`);
    });
    
    console.log('\n=== 测试报告结束 ===\n');
  }
}

// 导出测试器实例
export const memoryTester = new MemoryTester();

// 在开发环境下将测试器添加到全局
if (process.env.NODE_ENV === 'development') {
  (window as any).memoryTester = memoryTester;
}
