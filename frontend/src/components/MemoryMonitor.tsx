/**
 * 内存监控组件
 * 仅在开发环境下显示，用于监控应用的内存使用情况
 */

import React, { useState, useEffect } from 'react';
import { Box, Typography, Button, Collapse, IconButton, Alert } from '@mui/material';
import { ExpandMore, ExpandLess, Memory, CleaningServices } from '@mui/icons-material';
import { memoryManager } from '../utils/memoryManager';

interface MemoryStats {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
  timestamp: number;
}

const MemoryMonitor: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [memoryStats, setMemoryStats] = useState<MemoryStats | null>(null);
  const [isHighMemory, setIsHighMemory] = useState(false);
  const [cleanupCount, setCleanupCount] = useState(0);

  // 只在开发环境下显示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  useEffect(() => {
    const updateMemoryStats = () => {
      const stats = memoryManager.getCurrentMemoryStats();
      if (stats) {
        setMemoryStats(stats);
        
        // 检查内存使用率
        const usageRatio = stats.usedJSHeapSize / stats.jsHeapSizeLimit;
        setIsHighMemory(usageRatio > 0.7); // 70%阈值
      }
    };

    // 立即更新一次
    updateMemoryStats();

    // 每5秒更新一次
    const interval = setInterval(updateMemoryStats, 5000);

    return () => clearInterval(interval);
  }, []);

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getUsagePercentage = (): number => {
    if (!memoryStats) return 0;
    return (memoryStats.usedJSHeapSize / memoryStats.jsHeapSizeLimit) * 100;
  };

  const handleForceCleanup = () => {
    memoryManager.executeCleanup(true);
    setCleanupCount(prev => prev + 1);
  };

  const getMemoryHistory = () => {
    return memoryManager.getMemoryHistory();
  };

  if (!memoryStats) {
    return null;
  }

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 10,
        right: 10,
        zIndex: 9999,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        borderRadius: 1,
        p: 1,
        minWidth: 200,
        maxWidth: 400,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          cursor: 'pointer',
        }}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Memory fontSize="small" />
          <Typography variant="caption">
            内存: {formatBytes(memoryStats.usedJSHeapSize)}
          </Typography>
        </Box>
        <IconButton size="small" sx={{ color: 'white' }}>
          {isExpanded ? <ExpandLess /> : <ExpandMore />}
        </IconButton>
      </Box>

      <Collapse in={isExpanded}>
        <Box sx={{ mt: 1 }}>
          {isHighMemory && (
            <Alert severity="warning" sx={{ mb: 1, fontSize: '0.75rem' }}>
              内存使用率过高！
            </Alert>
          )}

          <Typography variant="caption" display="block">
            已用: {formatBytes(memoryStats.usedJSHeapSize)}
          </Typography>
          <Typography variant="caption" display="block">
            总计: {formatBytes(memoryStats.totalJSHeapSize)}
          </Typography>
          <Typography variant="caption" display="block">
            限制: {formatBytes(memoryStats.jsHeapSizeLimit)}
          </Typography>
          <Typography variant="caption" display="block">
            使用率: {getUsagePercentage().toFixed(1)}%
          </Typography>

          <Box
            sx={{
              width: '100%',
              height: 8,
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              borderRadius: 1,
              mt: 1,
              mb: 1,
            }}
          >
            <Box
              sx={{
                width: `${getUsagePercentage()}%`,
                height: '100%',
                backgroundColor: isHighMemory ? '#ff5722' : '#4caf50',
                borderRadius: 1,
                transition: 'all 0.3s ease',
              }}
            />
          </Box>

          <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
            <Button
              size="small"
              variant="outlined"
              startIcon={<CleaningServices />}
              onClick={handleForceCleanup}
              sx={{
                color: 'white',
                borderColor: 'white',
                fontSize: '0.7rem',
                '&:hover': {
                  borderColor: '#4caf50',
                  color: '#4caf50',
                },
              }}
            >
              清理 ({cleanupCount})
            </Button>
          </Box>

          <Typography variant="caption" display="block" sx={{ mt: 1, opacity: 0.7 }}>
            更新时间: {new Date(memoryStats.timestamp).toLocaleTimeString()}
          </Typography>

          {/* 内存历史图表（简化版） */}
          <Box sx={{ mt: 1 }}>
            <Typography variant="caption" display="block" sx={{ mb: 0.5 }}>
              内存趋势:
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'end', height: 30, gap: 1 }}>
              {getMemoryHistory().slice(-20).map((stat, index) => {
                const percentage = (stat.usedJSHeapSize / stat.jsHeapSizeLimit) * 100;
                return (
                  <Box
                    key={index}
                    sx={{
                      width: 3,
                      height: `${Math.max(percentage / 100 * 30, 2)}px`,
                      backgroundColor: percentage > 70 ? '#ff5722' : '#4caf50',
                      borderRadius: 0.5,
                    }}
                  />
                );
              })}
            </Box>
          </Box>
        </Box>
      </Collapse>
    </Box>
  );
};

export default MemoryMonitor;
