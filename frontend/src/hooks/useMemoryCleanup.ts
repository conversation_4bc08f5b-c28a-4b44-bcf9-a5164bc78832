/**
 * 内存清理Hook
 * 提供组件级别的内存管理和清理功能
 */

import { useEffect, useRef, useCallback } from 'react';
import { memoryManager } from '../utils/memoryManager';
import { cleanupEventListeners, cleanupTimers, cleanupAnimationFrames } from '../utils/cleanupUtils';

interface CleanupItem {
  type: 'event' | 'timer' | 'animation' | 'custom';
  data: any;
}

interface UseMemoryCleanupOptions {
  componentName?: string;
  enableAutoCleanup?: boolean;
  cleanupOnUnmount?: boolean;
}

export const useMemoryCleanup = (options: UseMemoryCleanupOptions = {}) => {
  const {
    componentName = 'UnknownComponent',
    enableAutoCleanup = true,
    cleanupOnUnmount = true
  } = options;

  const cleanupItemsRef = useRef<CleanupItem[]>([]);
  const eventListenersRef = useRef<Array<{ target: EventTarget; type: string; listener: EventListener; options?: boolean | AddEventListenerOptions }>>([]);
  const timersRef = useRef<Array<number | NodeJS.Timeout>>([]);
  const animationFramesRef = useRef<number[]>([]);
  const customCleanupRef = useRef<Array<() => void>>([]);

  /**
   * 注册事件监听器
   */
  const registerEventListener = useCallback((
    target: EventTarget,
    type: string,
    listener: EventListener,
    options?: boolean | AddEventListenerOptions
  ) => {
    target.addEventListener(type, listener, options);
    eventListenersRef.current.push({ target, type, listener, options });
  }, []);

  /**
   * 注册定时器
   */
  const registerTimer = useCallback((timer: number | NodeJS.Timeout) => {
    timersRef.current.push(timer);
    return timer;
  }, []);

  /**
   * 注册动画帧
   */
  const registerAnimationFrame = useCallback((frameId: number) => {
    animationFramesRef.current.push(frameId);
    return frameId;
  }, []);

  /**
   * 注册自定义清理函数
   */
  const registerCustomCleanup = useCallback((cleanup: () => void) => {
    customCleanupRef.current.push(cleanup);
  }, []);

  /**
   * 执行所有清理
   */
  const performCleanup = useCallback(() => {
    console.log(`[${componentName}] 开始执行内存清理`);

    // 清理事件监听器
    if (eventListenersRef.current.length > 0) {
      cleanupEventListeners(window, eventListenersRef.current);
      eventListenersRef.current = [];
    }

    // 清理定时器
    if (timersRef.current.length > 0) {
      cleanupTimers(timersRef.current);
      timersRef.current = [];
    }

    // 清理动画帧
    if (animationFramesRef.current.length > 0) {
      cleanupAnimationFrames(animationFramesRef.current);
      animationFramesRef.current = [];
    }

    // 执行自定义清理
    customCleanupRef.current.forEach(cleanup => {
      try {
        cleanup();
      } catch (error) {
        console.warn(`[${componentName}] 自定义清理函数执行失败:`, error);
      }
    });
    customCleanupRef.current = [];

    console.log(`[${componentName}] 内存清理完成`);
  }, [componentName]);

  /**
   * 强制清理
   */
  const forceCleanup = useCallback(() => {
    performCleanup();
    // 触发全局内存清理
    memoryManager.executeCleanup(true);
  }, [performCleanup]);

  // 注册到全局内存管理器
  useEffect(() => {
    if (enableAutoCleanup) {
      const cleanupName = `component-${componentName}-${Date.now()}`;
      memoryManager.registerCleanup(cleanupName, performCleanup, 4);

      return () => {
        memoryManager.unregisterCleanup(cleanupName);
      };
    }
  }, [componentName, enableAutoCleanup, performCleanup]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (cleanupOnUnmount) {
        performCleanup();
      }
    };
  }, [cleanupOnUnmount, performCleanup]);

  return {
    registerEventListener,
    registerTimer,
    registerAnimationFrame,
    registerCustomCleanup,
    performCleanup,
    forceCleanup,
    // 便捷方法
    setTimeout: (callback: () => void, delay: number) => {
      const timer = setTimeout(callback, delay);
      return registerTimer(timer);
    },
    setInterval: (callback: () => void, delay: number) => {
      const timer = setInterval(callback, delay);
      return registerTimer(timer);
    },
    requestAnimationFrame: (callback: FrameRequestCallback) => {
      const frameId = requestAnimationFrame(callback);
      return registerAnimationFrame(frameId);
    },
    addEventListener: registerEventListener,
  };
};

/**
 * 简化版本的内存清理Hook，只处理基本的清理需求
 */
export const useBasicCleanup = (componentName?: string) => {
  return useMemoryCleanup({
    componentName,
    enableAutoCleanup: false,
    cleanupOnUnmount: true
  });
};

/**
 * 高级版本的内存清理Hook，启用所有功能
 */
export const useAdvancedCleanup = (componentName?: string) => {
  return useMemoryCleanup({
    componentName,
    enableAutoCleanup: true,
    cleanupOnUnmount: true
  });
};
