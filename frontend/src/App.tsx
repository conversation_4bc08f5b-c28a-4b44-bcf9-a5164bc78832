import React, { useEffect } from "react";
import { CssBaseline } from "@mui/material";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { StoreProvider as ElementProvideo } from "./store";
import { StoreProvider } from "./store/store-context";
import Editor from "./editor/Editor";
import Dashboard from "./components/Dashboard";
import { CustomThemeProvider } from "./theme/ThemeContext";
import { LanguageProvider } from "./i18n/LanguageContext";
import LoadingOverlay from "./components/LoadingOverlay";
import MemoryMonitor from "./components/MemoryMonitor";
import { cleanupGifAnimations } from "./utils/fabricGif";
import { memoryManager } from "./utils/memoryManager";
import {
  registerCleanupFunctions,
  performGeneralCleanup,
} from "./utils/cleanupUtils";

const App: React.FC = () => {
  useEffect(() => {
    // 初始化内存管理器
    console.log("[App] 初始化内存管理器");

    // 注册清理函数
    registerCleanupFunctions();

    // 注册GIF清理函数
    memoryManager.registerCleanup("gif-animations", cleanupGifAnimations, 8);

    // 注册通用清理函数
    memoryManager.registerCleanup("general-cleanup", performGeneralCleanup, 2);

    // 开始内存监控（开发环境下更频繁监控）
    const monitorInterval =
      process.env.NODE_ENV === "development" ? 15000 : 30000;
    memoryManager.startMonitoring(monitorInterval);

    // 设置内存阈值（开发环境下更严格）
    const memoryThreshold = process.env.NODE_ENV === "development" ? 0.7 : 0.8;
    memoryManager.setMemoryThreshold(memoryThreshold);

    // 应用关闭时清理资源
    const handleBeforeUnload = () => {
      console.log("[App] 应用即将关闭，执行清理...");
      memoryManager.executeCleanup(true);
    };

    // 页面可见性变化时的清理
    const handleVisibilityChange = () => {
      if (document.hidden) {
        console.log("[App] 页面隐藏，执行轻量清理");
        memoryManager.executeCleanup(false);
      }
    };

    // 内存压力事件监听
    const handleMemoryPressure = () => {
      console.warn("[App] 检测到内存压力，执行强制清理");
      memoryManager.executeCleanup(true);
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    document.addEventListener("visibilitychange", handleVisibilityChange);

    // 监听内存压力事件（如果支持）
    if ("memory" in navigator) {
      window.addEventListener("memory-pressure", handleMemoryPressure);
    }

    return () => {
      console.log("[App] 组件卸载，清理所有资源");

      // 移除事件监听器
      window.removeEventListener("beforeunload", handleBeforeUnload);
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      if ("memory" in navigator) {
        window.removeEventListener("memory-pressure", handleMemoryPressure);
      }

      // 销毁内存管理器
      memoryManager.destroy();
    };
  }, []);

  return (
    <LanguageProvider>
      <CustomThemeProvider>
        <CssBaseline />
        <Router>
          <StoreProvider>
            <ElementProvideo>
              <LoadingOverlay />
              <MemoryMonitor />
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/editor" element={<Editor />} />
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </ElementProvideo>
          </StoreProvider>
        </Router>
      </CustomThemeProvider>
    </LanguageProvider>
  );
};

export default App;
