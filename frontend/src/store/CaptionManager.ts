import { makeAutoObservable } from "mobx";
import { Caption, CaptionStyle } from "../types";
import { fixAllCaptionCollisions } from "../editor/timeline/utils";
import { Store } from "./Store";
import { fabric } from "fabric";
import { TextboxWithPadding } from "../utils/fabric-utils";
import { CONSTANTS } from "./constants";

export class CaptionManager {
  private store: Store;
  captions: Caption[] = [];
  // 字幕显示对象（改为公开属性，以便其他组件可以访问）
  captionTextObject: fabric.TextboxWithPadding | null = null;
  // 当前选中的字幕ID
  private selectedCaptionId: string | null = null;
  // 全局字幕样式
  globalCaptionStyle: CaptionStyle;

  constructor(store: Store) {
    this.store = store;
    // Initialize with empty captions array
    this.captions = [];

    // 初始化全局字幕样式
    this.resetGlobalCaptionStyle();

    makeAutoObservable(this);
  }

  /**
   * 重置全局字幕样式为默认值
   */
  resetGlobalCaptionStyle(): void {
    const captionDefaults = CONSTANTS.CAPTION_STYLE;
    this.globalCaptionStyle = {
      fontSize: captionDefaults.DEFAULT_FONT_SIZE,
      fontFamily: captionDefaults.DEFAULT_FONT_FAMILY,
      fontColor: captionDefaults.DEFAULT_FONT_COLOR,
      fontWeight: captionDefaults.DEFAULT_FONT_WEIGHT,
      textAlign: captionDefaults.DEFAULT_TEXT_ALIGN,
      lineHeight: captionDefaults.DEFAULT_LINE_HEIGHT,
      charSpacing: captionDefaults.DEFAULT_CHAR_SPACING,
      styles: [...captionDefaults.DEFAULT_STYLES],
      strokeWidth: captionDefaults.DEFAULT_STROKE_WIDTH,
      strokeColor: captionDefaults.DEFAULT_STROKE_COLOR,
      shadowColor: captionDefaults.DEFAULT_SHADOW_COLOR,
      shadowBlur: captionDefaults.DEFAULT_SHADOW_BLUR,
      shadowOffsetX: captionDefaults.DEFAULT_SHADOW_OFFSET_X,
      shadowOffsetY: captionDefaults.DEFAULT_SHADOW_OFFSET_Y,
      backgroundColor: captionDefaults.DEFAULT_BACKGROUND_COLOR,
      useGradient: captionDefaults.DEFAULT_USE_GRADIENT,
      gradientColors: [...captionDefaults.DEFAULT_GRADIENT_COLORS],
      // 默认位置信息 - 改为以背景框左上角为基准
      positionX: captionDefaults.DEFAULT_POSITION_X, // 相对于画布左边缘的偏移
      positionY: captionDefaults.DEFAULT_POSITION_Y, // 相对于画布顶部的偏移
      originX: captionDefaults.DEFAULT_ORIGIN_X, // 保持水平居中对齐
      originY: captionDefaults.DEFAULT_ORIGIN_Y, // 保持垂直居中对齐，但使用左上角坐标系统
      // 默认变换属性
      scaleX: captionDefaults.DEFAULT_SCALE_X,
      scaleY: captionDefaults.DEFAULT_SCALE_Y,
      width: undefined, // 使用默认宽度
    };
  }

  /**
   * 将时间字符串格式化为标准的 "00:00:00" 格式
   * @param timeStr 输入的时间字符串
   * @returns 格式化后的时间字符串 (HH:MM:SS)
   */
  formatTimeString(timeStr: string): string {
    // 移除所有非数字字符
    const digits = timeStr.replace(/\D/g, "");

    // 补零确保至少有6位数字
    const paddedDigits = digits.padStart(6, "0");

    // 格式化为 "00:00:00"
    const hours = paddedDigits.slice(0, 2);
    const minutes = paddedDigits.slice(2, 4);
    const seconds = paddedDigits.slice(4, 6);

    return `${hours}:${minutes}:${seconds}`;
  }

  /**
   * 添加新字幕
   * 新字幕将被添加到最后一个字幕之后，持续时间为3秒
   * @returns 新添加的字幕对象
   */
  addCaption(): Caption {
    // 获取最后一个字幕，如果没有则使用默认值
    const lastCaption =
      this.captions.length > 0 ? this.captions[this.captions.length - 1] : null;
    const newStartTime = lastCaption ? lastCaption.endTime : "00:00:00";

    // 解析时间部分
    const endTimeParts = newStartTime.split(":").map(Number);

    // 添加3秒作为结束时间
    endTimeParts[2] += 3;

    // 处理进位
    if (endTimeParts[2] >= 60) {
      endTimeParts[2] -= 60;
      endTimeParts[1] += 1;
    }
    if (endTimeParts[1] >= 60) {
      endTimeParts[1] -= 60;
      endTimeParts[0] += 1;
    }

    // 格式化新的结束时间
    const newEndTime = endTimeParts
      .map((part) => part.toString().padStart(2, "0"))
      .join(":");

    // 创建新字幕
    const newId = Date.now().toString();
    const newCaption: Caption = {
      id: newId,
      startTime: newStartTime,
      endTime: newEndTime,
      text: "Default",
      isSelected: true,
    };

    // 更新所有字幕，取消选择状态，只选择新字幕
    const updatedCaptions = this.captions.map((caption) => ({
      ...caption,
      isSelected: false,
    }));

    // 添加新字幕并保存更改
    this.captions = [...updatedCaptions, newCaption];

    // 更新最大时间，因为新增了字幕
    this.store.updateMaxTime();
    this.store.saveChange();

    // 跳转到新字幕的开始时间，让用户立即看到新添加的字幕
    const newStartTimeMs = this.timeStringToMilliseconds(newStartTime);
    this.store.handleSeek(newStartTimeMs);

    return newCaption;
  }

  /**
   * 删除指定ID的字幕
   * @param id 要删除的字幕ID
   * @returns 删除后的字幕数组
   */
  deleteCaption(id: string): Caption[] {
    this.captions = this.captions.filter((caption) => caption.id !== id);

    // 如果删除后没有字幕了，重置样式
    if (this.captions.length === 0) {
      this.resetGlobalCaptionStyle();

      // 移除画布中的字幕对象
      if (this.captionTextObject && this.store.canvas) {
        this.store.canvas.remove(this.captionTextObject);
        this.captionTextObject = null;
      }
    }

    // 更新最大时间，因为删除了字幕
    this.store.updateMaxTime();
    this.store.saveChange();
    return this.captions;
  }

  /**
   * 选择指定ID的字幕，取消选择其他字幕
   * @param id 要选择的字幕ID
   * @returns 更新后的字幕数组
   */
  selectCaption(id: string): Caption[] {
    this.captions = this.captions.map((caption) => ({
      ...caption,
      isSelected: caption.id === id,
    }));

    // 更新选中的字幕ID
    this.selectedCaptionId = id;

    // 同步Canvas中的字幕选中状态
    this.syncCanvasCaptionSelection(id);

    return this.captions;
  }

  /**
   * 同步Canvas中字幕的选中状态
   * @param selectedId 选中的字幕ID
   */
  private syncCanvasCaptionSelection(selectedId: string | null) {
    if (!this.store.canvas || !this.captionTextObject) return;

    // 获取选中的字幕
    const selectedCaption = selectedId
      ? this.captions.find((caption) => caption.id === selectedId)
      : null;

    if (selectedCaption) {
      // 如果有选中的字幕，始终在画布上选中字幕对象
      this.store.canvas.setActiveObject(this.captionTextObject);
      // 注意：不在这里调用 setSelectedElement(null)，因为已经在 Store.selectCaption 中处理了
      this.store.canvas.requestRenderAll();
    } else {
      // 取消Canvas中字幕的选中状态
      if (this.store.canvas.getActiveObject() === this.captionTextObject) {
        this.store.canvas.discardActiveObject();
      }
    }
  }

  /**
   * 更新字幕的指定字段
   * @param id 要更新的字幕ID
   * @param field 要更新的字段
   * @param value 新值
   * @returns 更新后的字幕对象，如果未找到则返回undefined
   */
  updateCaption(
    id: string,
    field: keyof Caption,
    value: string
  ): Caption | undefined {
    // 时间字段特殊处理
    if (field === "startTime" || field === "endTime") {
      // 如果值已经是正确的格式 (HH:MM:SS)，直接使用
      const timeRegex = /^([0-9]{2}):([0-9]{2}):([0-9]{2})$/;
      if (timeRegex.test(value)) {
        this.captions = this.captions.map((caption) =>
          caption.id === id ? { ...caption, [field]: value } : caption
        );

        // 如果更新的是时间字段，需要更新最大时间
        this.store.updateMaxTime();

        // 更新时间字段后，检查当前时间线指示器位置是否需要更新字幕显示
        this.updateCurrentCaption(this.store.currentTimeInMs);

        this.store.saveChange();
        return this.captions.find((caption) => caption.id === id);
      }

      // 如果值是数字（来自拖拽操作），转换为时间格式
      const numericValue = Number(value);
      if (!isNaN(numericValue)) {
        const hours = Math.floor(numericValue / 3600000);
        const minutes = Math.floor((numericValue % 3600000) / 60000);
        const seconds = Math.floor((numericValue % 60000) / 1000);
        const formattedTime = `${hours.toString().padStart(2, "0")}:${minutes
          .toString()
          .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;

        this.captions = this.captions.map((caption) =>
          caption.id === id ? { ...caption, [field]: formattedTime } : caption
        );

        // 如果更新的是时间字段，需要更新最大时间
        this.store.updateMaxTime();

        // 更新时间字段后，检查当前时间线指示器位置是否需要更新字幕显示
        this.updateCurrentCaption(this.store.currentTimeInMs);

        this.store.saveChange();
        return this.captions.find((caption) => caption.id === id);
      }

      // 对于手动输入，允许输入但在失去焦点时强制格式化
      const typingRegex = /^([0-9]{0,2}):?([0-9]{0,2}):?([0-9]{0,2})$/;
      if (!typingRegex.test(value)) {
        return undefined; // 拒绝无效输入
      }
    }

    // 更新字幕
    this.captions = this.captions.map((caption) =>
      caption.id === id ? { ...caption, [field]: value } : caption
    );
    this.store.saveChange();
    return this.captions.find((caption) => caption.id === id);
  }

  /**
   * 根据startTime对字幕进行排序
   * @returns 排序后的字幕数组
   */
  sortCaptionsByStartTime(): Caption[] {
    // 进行排序
    const sortedCaptions = [...this.captions].sort((a, b) => {
      return this.timeToSeconds(a.startTime) - this.timeToSeconds(b.startTime);
    });

    // 更新字幕数组
    this.captions = sortedCaptions;
    return this.captions;
  }

  // 检测并处理所有字幕之间可能的重叠
  preventAllCaptionsOverlap(): void {
    // 如果没有字幕或只有一个字幕，不需要检查重叠
    if (this.captions.length <= 1) return;

    // 使用智能碰撞检测和修复函数
    const fixedCaptions = fixAllCaptionCollisions(this.captions);

    // 更新字幕数组
    this.captions = fixedCaptions;

    // 更新最大时间，因为字幕时间可能被修复
    this.store.updateMaxTime();
  }

  /**
   * 格式化字幕时间，确保符合标准格式
   * @param timeStr 输入的时间字符串
   * @returns 格式化后的时间字符串
   */
  formatCaptionTime(timeStr: string): string {
    return this.formatTimeString(timeStr);
  }

  /**
   * 在两个字幕之间添加新字幕
   * @param index 要插入的位置索引
   * @returns 新添加的字幕对象
   */
  addCaptionBetween(index: number): Caption {
    // 获取前后字幕
    const prevCaption = this.captions[index - 1];
    const nextCaption = this.captions[index];

    // 使用前一个字幕的结束时间作为新字幕的开始时间
    // 使用后一个字幕的开始时间作为新字幕的结束时间
    const prevEndTime = prevCaption.endTime;
    const nextStartTime = nextCaption.startTime;

    // 创建新字幕
    const newCaption: Caption = {
      id: Date.now().toString(),
      startTime: prevEndTime,
      endTime: nextStartTime,
      text: "",
      isSelected: true,
    };

    // 更新字幕数组
    const newCaptions = [...this.captions];
    newCaptions.splice(index, 0, newCaption);
    this.captions = newCaptions;

    // 更新最大时间，因为添加了新字幕
    this.store.updateMaxTime();
    this.store.saveChange();

    // 跳转到新字幕的开始时间，让用户立即看到新添加的字幕
    const newStartTimeMs = this.timeStringToMilliseconds(prevEndTime);
    this.store.handleSeek(newStartTimeMs);

    return newCaption;
  }

  /**
   * 合并两个相邻的字幕
   * @param index 后一个字幕的索引
   * @returns 合并后的字幕对象
   */
  mergeCaptions(index: number): Caption {
    // 获取要合并的两个字幕
    const prevCaption = this.captions[index - 1];
    const nextCaption = this.captions[index];

    // 创建合并后的字幕
    const mergedCaption: Caption = {
      id: Date.now().toString(),
      startTime: prevCaption.startTime,
      endTime: nextCaption.endTime,
      text: `${prevCaption.text} ${nextCaption.text}`.trim(),
      isSelected: true,
    };

    // 更新字幕数组
    const newCaptions = [...this.captions];
    newCaptions.splice(index - 1, 2, mergedCaption);
    this.captions = newCaptions;

    // 更新最大时间，因为合并了字幕
    this.store.updateMaxTime();
    this.store.saveChange();

    return mergedCaption;
  }

  /**
   * 取消选择所有字幕
   * @returns 更新后的字幕数组
   */
  deselectAllCaptions(): Caption[] {
    this.captions = this.captions.map((caption) => ({
      ...caption,
      isSelected: false,
    }));

    // 清除选中的字幕ID
    this.selectedCaptionId = null;

    // 同步Canvas中的字幕选中状态
    this.syncCanvasCaptionSelection(null);

    return this.captions;
  }

  /**
   * 选择所有字幕
   * @returns 更新后的字幕数组
   */
  selectAllCaptions(): Caption[] {
    if (this.captions.length === 0) {
      return this.captions;
    }

    // 将所有字幕设为选中状态
    this.captions = this.captions.map((caption) => ({
      ...caption,
      isSelected: true,
    }));

    // 设置第一个字幕为主要选中的字幕（用于Canvas同步）
    this.selectedCaptionId = this.captions[0].id;

    // 同步Canvas中的字幕选中状态
    this.syncCanvasCaptionSelection(this.selectedCaptionId);

    return this.captions;
  }

  /**
   * 切换字幕的选中状态（用于多选）
   * @param id 要切换的字幕ID
   * @returns 更新后的字幕数组
   */
  toggleCaptionSelection(id: string): Caption[] {
    const targetCaption = this.captions.find((caption) => caption.id === id);
    if (!targetCaption) {
      return this.captions;
    }

    const wasSelected = targetCaption.isSelected;

    // 切换目标字幕的选中状态
    this.captions = this.captions.map((caption) => ({
      ...caption,
      isSelected: caption.id === id ? !caption.isSelected : caption.isSelected,
    }));

    // 更新主要选中的字幕ID
    const selectedCaptions = this.captions.filter(
      (caption) => caption.isSelected
    );

    if (selectedCaptions.length > 0) {
      // 如果目标字幕现在是选中状态，将其设为主要选中
      if (!wasSelected) {
        this.selectedCaptionId = id;
      } else if (this.selectedCaptionId === id) {
        // 如果取消选择的是主要选中的字幕，选择第一个选中的字幕
        this.selectedCaptionId = selectedCaptions[0].id;
      }
    } else {
      this.selectedCaptionId = null;
    }

    // 同步Canvas中的字幕选中状态
    this.syncCanvasCaptionSelection(this.selectedCaptionId);

    return this.captions;
  }

  /**
   * 添加字幕到选中列表（用于多选）
   * @param id 要添加的字幕ID
   * @returns 更新后的字幕数组
   */
  addCaptionToSelection(id: string): Caption[] {
    this.captions = this.captions.map((caption) => ({
      ...caption,
      isSelected: caption.id === id ? true : caption.isSelected,
    }));

    // 设置为主要选中的字幕
    this.selectedCaptionId = id;

    // 同步Canvas中的字幕选中状态
    this.syncCanvasCaptionSelection(this.selectedCaptionId);

    return this.captions;
  }

  /**
   * 从选中列表中移除字幕（用于多选）
   * @param id 要移除的字幕ID
   * @returns 更新后的字幕数组
   */
  removeCaptionFromSelection(id: string): Caption[] {
    this.captions = this.captions.map((caption) => ({
      ...caption,
      isSelected: caption.id === id ? false : caption.isSelected,
    }));

    // 更新主要选中的字幕ID
    const selectedCaptions = this.captions.filter(
      (caption) => caption.isSelected
    );
    if (selectedCaptions.length > 0) {
      if (this.selectedCaptionId === id) {
        // 如果移除的是主要选中的字幕，选择第一个选中的字幕
        this.selectedCaptionId = selectedCaptions[0].id;
      }
    } else {
      this.selectedCaptionId = null;
    }

    // 同步Canvas中的字幕选中状态
    this.syncCanvasCaptionSelection(this.selectedCaptionId);

    return this.captions;
  }

  /**
   * 更新全局字幕样式
   * @param styleProps 样式属性对象
   */
  updateGlobalCaptionStyle(styleProps: Partial<CaptionStyle>): void {
    // 更新全局字幕样式
    this.globalCaptionStyle = {
      ...this.globalCaptionStyle,
      ...styleProps,
    };

    // 立即更新当前显示的字幕
    this.updateCurrentCaption(this.store.currentTimeInMs);

    this.store.saveChange();
  }

  /**
   * 仅更新全局字幕样式，不触发字幕重新创建（用于Canvas操作期间）
   * @param styleProps 样式属性对象
   */
  private updateGlobalCaptionStyleOnly(
    styleProps: Partial<CaptionStyle>
  ): void {
    // 更新全局字幕样式
    this.globalCaptionStyle = {
      ...this.globalCaptionStyle,
      ...styleProps,
    };

    // 不调用updateCurrentCaption，避免重新创建字幕对象
    this.store.saveChange();
  }

  /**
   * 获取当前选中的字幕
   * @returns 选中的字幕对象，如果没有则返回null
   */
  getSelectedCaption(): Caption | null {
    return this.captions.find((caption) => caption.isSelected) || null;
  }

  /**
   * 获取所有选中的字幕
   * @returns 选中的字幕数组
   */
  getSelectedCaptions(): Caption[] {
    return this.captions.filter((caption) => caption.isSelected);
  }

  /**
   * 检查字幕是否被选中
   * @param id 字幕ID
   * @returns 是否被选中
   */
  isCaptionSelected(id: string): boolean {
    return this.captions.some(
      (caption) => caption.id === id && caption.isSelected
    );
  }

  /**
   * 导出字幕为SRT格式
   * @returns SRT格式的字幕内容
   */
  exportCaptionsAsSRT(): string {
    // 按开始时间排序字幕
    const sortedCaptions = [...this.captions].sort((a, b) => {
      return this.timeToSeconds(a.startTime) - this.timeToSeconds(b.startTime);
    });

    // 转换为SRT格式
    return sortedCaptions
      .map((caption, index) => {
        // SRT索引
        const srtIndex = index + 1;

        // 转换HH:MM:SS为HH:MM:SS,000格式（SRT使用逗号作为小数分隔符）
        const startTime = this.convertToSRTTimeFormat(caption.startTime);
        const endTime = this.convertToSRTTimeFormat(caption.endTime);

        // 返回格式化的SRT条目
        return `${srtIndex}\n${startTime} --> ${endTime}\n${caption.text}\n`;
      })
      .join("\n");
  }

  /**
   * 将时间字符串转换为秒数
   * @param timeStr 时间字符串 (HH:MM:SS)
   * @returns 转换后的秒数
   */
  private timeToSeconds(timeStr: string): number {
    try {
      // 确保时间字符串格式正确 (HH:MM:SS)
      if (!timeStr.match(/^\d{2}:\d{2}:\d{2}$/)) {
        // 尝试格式化
        timeStr = this.formatTimeString(timeStr);
      }

      const [hours, minutes, seconds] = timeStr.split(":").map(Number);

      // 检查数值有效性
      if (isNaN(hours) || isNaN(minutes) || isNaN(seconds)) {
        console.warn(`无效的时间格式: ${timeStr}`);
        return 0;
      }

      return hours * 3600 + minutes * 60 + seconds;
    } catch (error) {
      console.warn(`转换时间字符串时出错: ${timeStr}`, error);
      return 0;
    }
  }

  /**
   * 将时间字符串转换为SRT格式 (HH:MM:SS,000)
   * @param timeStr 时间字符串 (HH:MM:SS)
   * @returns SRT格式的时间字符串
   */
  private convertToSRTTimeFormat(timeStr: string): string {
    const [hours, minutes, seconds] = timeStr.split(":");
    return `${hours}:${minutes}:${seconds},000`;
  }

  /**
   * 解析SRT内容并转换为Caption对象
   * @param srtContent SRT格式的字幕内容
   * @returns 导入的字幕数组
   * @throws 如果解析失败则抛出错误
   */
  importCaptionsFromSRT(srtContent: string): Caption[] {
    try {
      // 创建新的字幕数组
      const newCaptions: Caption[] = [];

      // SRT文件可能有不同的行结束格式
      // 标准化行结束符并按双换行分割获取单个字幕块
      const normalizedContent = srtContent
        .replace(/\r\n/g, "\n")
        .replace(/\r/g, "\n");
      const captionBlocks = normalizedContent.trim().split(/\n\n+/);

      // 如果没有找到有效的块，则退出
      if (captionBlocks.length === 0) {
        throw new Error("文件中未找到有效的字幕块");
      }

      for (const block of captionBlocks) {
        const lines = block.split(/\n/);

        // 跳过无效块（至少需要索引、时间线和文本）
        if (lines.length < 3) continue;

        // 跳过索引号（第一行）并获取时间线
        // 格式可以是：00:00:00,000 --> 00:00:00,000 或 00:00:00.000 --> 00:00:00.000
        const timelineMatch = lines[1].match(
          /(\d{2}:\d{2}:\d{2}[,\.]\d{3})\s+-->\s+(\d{2}:\d{2}:\d{2}[,\.]\d{3})/
        );
        if (!timelineMatch) continue;

        // 提取开始和结束时间
        let startTime = timelineMatch[1].replace(/[,\.]\d{3}$/, ""); // 移除毫秒
        let endTime = timelineMatch[2].replace(/[,\.]\d{3}$/, ""); // 移除毫秒

        // 获取文本内容（可能有多行）
        const textContent = lines.slice(2).join("\n").trim();

        // 如果没有文本内容则跳过
        if (!textContent) continue;

        // 创建新的Caption对象
        newCaptions.push({
          id: `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          startTime,
          endTime,
          text: textContent,
          isSelected: false,
        });
      }

      if (newCaptions.length === 0) {
        throw new Error("文件中未找到有效的字幕");
      }

      // 按开始时间排序字幕
      newCaptions.sort(
        (a, b) =>
          this.timeToSeconds(a.startTime) - this.timeToSeconds(b.startTime)
      );

      // 更新字幕数组
      this.captions = newCaptions;

      // 更新最大时间，因为导入了新字幕
      this.store.updateMaxTime();
      return this.captions;
    } catch (error) {
      console.error("解析SRT文件时出错:", error);
      throw new Error(`解析SRT文件失败: ${error.message}`);
    }
  }

  /**
   * 将内容下载为文件
   * @param content 文件内容
   * @param filename 文件名
   * @param mimeType MIME类型
   */
  private downloadFile(
    content: string,
    filename: string,
    mimeType: string
  ): void {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);

    const link = document.createElement("a");
    link.href = url;
    link.download = filename;

    // 添加到文档，点击，然后移除
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 清理URL对象
    setTimeout(() => URL.revokeObjectURL(url), 100);
  }

  /**
   * 导出并下载字幕为SRT文件
   */
  exportAndDownloadCaptionsAsSRT(): void {
    const srtContent = this.exportCaptionsAsSRT();
    const filename = `${this.store.projectName.replace(
      /\s+/g,
      "_"
    )}_captions.srt`;
    this.downloadFile(srtContent, filename, "text/plain;charset=utf-8");
  }

  /**
   * 设置字幕数组（用于序列化和从localStorage加载）
   * @param captions 字幕数组
   * @returns 设置后的字幕数组
   */
  setCaptions(captions: Caption[]): Caption[] {
    this.captions = captions;

    // 如果设置为空数组，同时重置样式
    if (captions.length === 0) {
      this.resetGlobalCaptionStyle();

      // 移除画布中的字幕对象
      if (this.captionTextObject && this.store.canvas) {
        this.store.canvas.remove(this.captionTextObject);
        this.captionTextObject = null;
      }
    } else {
      // 如果设置了非空字幕数组，根据当前时间更新字幕显示
      // 这确保了页面刷新后字幕能够立即显示（如果当前时间在字幕范围内）
      if (this.store.canvas) {
        const currentTime = this.store.currentTimeInMs;
        this.updateCurrentCaption(currentTime);

        // 如果当前时间找不到字幕，且字幕数组不为空，自动跳转到第一个字幕的开始时间
        // 这解决了页面刷新后字幕不显示的问题
        const currentCaption = this.findCurrentCaption(currentTime);
        if (!currentCaption && captions.length > 0) {
          const firstCaption = this.getFirstCaption();
          if (firstCaption) {
            const firstCaptionStartTime = this.timeStringToMilliseconds(
              firstCaption.startTime
            );
            // 如果第一个字幕从0开始，直接更新字幕显示；否则跳转到字幕开始时间
            if (firstCaptionStartTime === 0) {
              // 直接更新字幕显示，不需要跳转
              this.updateCurrentCaption(0);
            } else {
              this.store.handleSeek(firstCaptionStartTime);
            }
          }
        }
      }
    }

    // 更新最大时间，因为设置了新的字幕数组
    this.store.updateMaxTime();
    this.store.saveChange();
    return this.captions;
  }

  /**
   * 清除所有字幕
   * @returns 空字幕数组
   */
  clearAllCaptions(): Caption[] {
    this.captions = [];

    // 重置全局字幕样式为默认值
    this.resetGlobalCaptionStyle();

    // 移除画布中的字幕对象
    if (this.captionTextObject && this.store.canvas) {
      this.store.canvas.remove(this.captionTextObject);
      this.captionTextObject = null;
    }

    // 更新最大时间，因为清除了所有字幕
    this.store.updateMaxTime();
    this.store.saveChange();
    return this.captions;
  }

  /**
   * 将时间字符串转换为毫秒
   * @param timeStr 时间字符串 (HH:MM:SS)
   * @returns 毫秒数
   */
  timeStringToMilliseconds(timeStr: string): number {
    const parts = timeStr.split(":").map(Number);
    if (parts.length !== 3) {
      console.warn(`无效的时间格式: ${timeStr}`);
      return 0;
    }

    const [hours, minutes, seconds] = parts;
    return (hours * 3600 + minutes * 60 + seconds) * 1000;
  }

  /**
   * 查找当前时间应该显示的字幕
   * @param currentTimeMs 当前时间（毫秒）
   * @returns 当前应显示的字幕，如果没有则返回null
   */
  findCurrentCaption(currentTimeMs: number): Caption | null {
    if (!this.captions || this.captions.length === 0) {
      return null;
    }

    // 使用timeStringToMilliseconds工具函数将字幕时间转换为毫秒，以确保精确比较
    for (const caption of this.captions) {
      // 将字幕的开始和结束时间转换为毫秒
      const startTimeMs = this.timeStringToMilliseconds(caption.startTime);
      const endTimeMs = this.timeStringToMilliseconds(caption.endTime);

      // 检查当前时间是否在字幕的时间范围内
      // 使用严格的比较，确保在结束时间后字幕不再显示
      if (currentTimeMs >= startTimeMs && currentTimeMs < endTimeMs) {
        return caption;
      }
    }

    return null;
  }

  /**
   * 更新当前时间的字幕显示
   * @param currentTimeMs 当前时间（毫秒）
   */
  updateCurrentCaption(currentTimeMs: number) {
    if (!this.store.canvas) {
      console.log("updateCurrentCaption: Canvas 不存在，无法更新字幕显示");
      return;
    }

    // 查找当前时间应该显示的字幕
    const currentCaption = this.findCurrentCaption(currentTimeMs);

    // 如果没有找到字幕，移除现有的字幕显示
    if (!currentCaption) {
      if (this.captionTextObject) {
        this.store.canvas.remove(this.captionTextObject);
        this.captionTextObject = null;
      }
      return;
    }

    // 如果找到了字幕，创建或更新字幕显示
    const captionText = currentCaption.text;

    // 获取字幕样式属性
    const captionStyles = this.getCaptionStyles();

    // **重要修复**: 确保宽度计算一致
    // 先确定fabric对象的实际宽度，然后用这个宽度来计算位置
    const fabricObjectWidth =
      captionStyles.width ||
      this.store.canvasWidth * CONSTANTS.CAPTION_STYLE.DEFAULT_WIDTH_RATIO;

    // 计算字幕位置

    // 计算基础位置
    let captionLeft: number;
    let captionTop: number;

    // 根据originX计算水平位置 - 改为以背景框左上角为基准
    // 使用限制后的宽度进行位置计算
    const maxWidth = Math.min(fabricObjectWidth, this.store.canvasWidth * 0.8);
    switch (captionStyles.originX) {
      case "left":
        captionLeft = 0 + (this.globalCaptionStyle.positionX || 0);
        break;
      case "right":
        // 使用限制后的宽度（包含缩放）
        const scaledWidth = maxWidth * (captionStyles.scaleX || 1);
        captionLeft =
          this.store.canvasWidth -
          scaledWidth +
          (this.globalCaptionStyle.positionX || 0);
        break;
      case "center":
      default:
        // 使用限制后的宽度（包含缩放）进行居中计算
        const centerScaledWidth = maxWidth * (captionStyles.scaleX || 1);
        captionLeft =
          (this.store.canvasWidth - centerScaledWidth) / 2 +
          (this.globalCaptionStyle.positionX || 0);
        break;
    }

    // 根据originY计算垂直位置 - 改为以背景框左上角为基准
    switch (captionStyles.originY) {
      case "top":
        captionTop = 0 + (this.globalCaptionStyle.positionY || 0);
        break;
      case "center":
        // 计算背景框高度，然后居中放置
        const backgroundHeight = this.calculateCaptionBackgroundHeight(
          captionText,
          captionStyles
        );
        captionTop =
          (this.store.canvasHeight - backgroundHeight) / 2 +
          (this.globalCaptionStyle.positionY || 0);
        break;
      case "bottom":
      default:
        // 计算背景框高度，然后从底部放置
        const bottomBackgroundHeight = this.calculateCaptionBackgroundHeight(
          captionText,
          captionStyles
        );
        const bottomMargin = Math.round(this.store.canvasHeight * 0.02);
        captionTop =
          this.store.canvasHeight -
          bottomMargin -
          bottomBackgroundHeight +
          (this.globalCaptionStyle.positionY || 0);
        break;
    }

    if (!this.captionTextObject) {
      // 创建新的字幕文本对象，使用TextboxWithPadding以获得与文本元素一致的渲染效果

      // 使用TextboxWithPadding，但限制宽度以防止超出画布
      const maxWidth = Math.min(
        fabricObjectWidth,
        this.store.canvasWidth * 0.8
      ); // 限制为画布宽度的80%

      this.captionTextObject = new fabric.TextboxWithPadding(captionText, {
        left: captionLeft,
        top: captionTop,
        fontSize: captionStyles.fontSize,
        fontFamily: captionStyles.fontFamily,
        textAlign: captionStyles.textAlign,
        originX: "left", // 固定为左对齐，以背景框左上角为基准
        originY: "top", // 固定为顶部对齐，以背景框左上角为基准
        lineHeight: captionStyles.lineHeight,
        charSpacing: captionStyles.charSpacing,
        // 应用文本样式
        fontWeight: (captionStyles.styles?.includes("bold") ||
        captionStyles.fontWeight === 700
          ? "bold"
          : "normal") as "normal" | "bold",
        fontStyle: (captionStyles.styles?.includes("italic")
          ? "italic"
          : "normal") as "" | "normal" | "italic" | "oblique",
        underline: captionStyles.styles?.includes("underline"),
        linethrough: captionStyles.styles?.includes("strikethrough"),
        // 文本颜色
        fill:
          captionStyles.fontColor || CONSTANTS.CAPTION_STYLE.DEFAULT_FONT_COLOR,
        // 描边属性
        strokeWidth: captionStyles.strokeWidth || 0,
        stroke: captionStyles.strokeColor || "#000000",
        // 背景色
        backgroundColor: captionStyles.backgroundColor,
        // 可见性和交互属性
        visible: true, // 确保字幕对象可见
        selectable: true,
        evented: true,
        editable: true,
        cursorColor: CONSTANTS.CAPTION_STYLE.DEFAULT_CURSOR_COLOR,
        cursorWidth: CONSTANTS.CAPTION_STYLE.DEFAULT_CURSOR_WIDTH,
        width: maxWidth, // 使用限制后的宽度
        scaleX: captionStyles.scaleX || CONSTANTS.CAPTION_STYLE.DEFAULT_SCALE_X,
        scaleY: captionStyles.scaleY || CONSTANTS.CAPTION_STYLE.DEFAULT_SCALE_Y,
        padding: CONSTANTS.CAPTION_STYLE.DEFAULT_PADDING,
        objectCaching: false, // 与文本元素保持一致
        //@ts-ignore
        rx: CONSTANTS.CAPTION_STYLE.DEFAULT_BORDER_RADIUS,
        //@ts-ignore
        ry: CONSTANTS.CAPTION_STYLE.DEFAULT_BORDER_RADIUS,
      });

      // 应用高级样式（渐变、阴影）
      this._applyCaptionAdvancedStyles(this.captionTextObject, captionStyles);

      // 添加文本编辑完成事件监听器
      this.captionTextObject.on("editing:exited", () => {
        const newText = this.captionTextObject.text;
        if (currentCaption && newText !== currentCaption.text) {
          this.updateCaption(currentCaption.id, "text", newText);
        }
      });

      // 添加对象修改事件监听器（处理scale、width等变换属性）
      this.captionTextObject.on("modified", () => {
        this.updateCaptionPropertiesFromCanvas();
        this.store.saveChange();
      });

      // 添加拖拽事件监听器
      this.captionTextObject.on("moving", () => {
        this.updateCaptionPositionFromCanvasOnly();
      });

      this.captionTextObject.on("moved", () => {
        this.updateCaptionPositionFromCanvasOnly();
        this.store.saveChange();
      });

      this.store.canvas.add(this.captionTextObject);

      // 将字幕对象置于最前面
      this.captionTextObject.bringToFront();
    } else {
      // 更新现有字幕文本和位置
      this.captionTextObject.set({
        text: captionText,
        left: captionLeft,
        top: captionTop,
        fontSize: captionStyles.fontSize,
        fontFamily: captionStyles.fontFamily,
        textAlign: captionStyles.textAlign,
        lineHeight: captionStyles.lineHeight,
        charSpacing: captionStyles.charSpacing,
        // 应用文本样式
        fontWeight: (captionStyles.styles?.includes("bold") ||
        captionStyles.fontWeight === 700
          ? "bold"
          : "normal") as "normal" | "bold",
        fontStyle: (captionStyles.styles?.includes("italic")
          ? "italic"
          : "normal") as "" | "normal" | "italic" | "oblique",
        underline: captionStyles.styles?.includes("underline"),
        linethrough: captionStyles.styles?.includes("strikethrough"),
        // 描边属性
        strokeWidth: captionStyles.strokeWidth || 0,
        stroke: captionStyles.strokeColor || "#000000",
        width: fabricObjectWidth, // **修复**: 使用计算位置时相同的宽度
      });

      // 应用高级样式（渐变、阴影）
      this._applyCaptionAdvancedStyles(this.captionTextObject, captionStyles);

      // 应用缩放属性（如果存在）
      if (captionStyles.scaleX !== undefined) {
        this.captionTextObject.scaleX = captionStyles.scaleX;
      }
      if (captionStyles.scaleY !== undefined) {
        this.captionTextObject.scaleY = captionStyles.scaleY;
      }
    }

    // 确保字幕始终在最上层
    if (this.captionTextObject) {
      this.captionTextObject.bringToFront();

      // 如果当前字幕是选中状态，同步Canvas选中状态
      if (currentCaption && currentCaption.isSelected) {
        this.store.canvas.setActiveObject(this.captionTextObject);
      }
    }

    // 重新渲染画布以确保字幕显示
    this.store.canvas.requestRenderAll();
  }

  /**
   * 仅从Canvas更新位置信息，不触发字幕重新创建（用于Canvas操作期间）
   */
  private updateCaptionPositionFromCanvasOnly(): void {
    if (!this.captionTextObject) return;

    const currentLeft = this.captionTextObject.left || 0;
    const currentTop = this.captionTextObject.top || 0;
    const originX = this.captionTextObject.originX || "center";
    const originY = this.captionTextObject.originY || "bottom";

    // 计算相对于默认位置的偏移
    let positionX = 0;
    let positionY = 0;

    // 计算水平偏移（相对于左上角坐标系统）
    // 由于originX固定为left，直接使用currentLeft作为基准
    const globalOriginX = this.globalCaptionStyle.originX || "left";
    switch (globalOriginX) {
      case "left":
        positionX = currentLeft - 0;
        break;
      case "right":
        // 从右边缘计算偏移
        positionX = currentLeft - this.store.canvasWidth;
        break;
      case "center":
      default:
        // 从中心计算偏移
        positionX = currentLeft - this.store.canvasWidth / 2;
        break;
    }

    // 计算垂直偏移（相对于左上角坐标系统）
    // 由于originY固定为top，直接使用currentTop作为基准
    const globalOriginY = this.globalCaptionStyle.originY || "top";
    switch (globalOriginY) {
      case "top":
        positionY = currentTop - 0;
        break;
      case "center":
        positionY = currentTop - this.store.canvasHeight / 2;
        break;
      case "bottom":
      default:
        // 从底部计算偏移
        const bottomMargin = Math.round(this.store.canvasHeight * 0.02);
        const defaultTop = this.store.canvasHeight - bottomMargin;
        positionY = currentTop - defaultTop;
        break;
    }

    // 直接更新全局样式，避免触发字幕重新创建
    this.updateGlobalCaptionStyleOnly({
      positionX: Math.round(positionX),
      positionY: Math.round(positionY),
      originX: originX as "left" | "center" | "right",
      originY: originY as "top" | "center" | "bottom",
    });
  }

  /**
   * 从Canvas更新字幕属性到全局样式（包括scale、width等变换属性）
   * 与文字元素的控制框处理逻辑保持一致
   */
  private updateCaptionPropertiesFromCanvas(): void {
    if (!this.captionTextObject) return;

    // 更新位置（但不触发字幕重新创建）
    this.updateCaptionPositionFromCanvasOnly();

    // 获取当前fabric对象的属性
    const fabricObject = this.captionTextObject;

    // 更新scale属性 - 与文字元素处理逻辑一致
    const scaleX = fabricObject.scaleX || 1;
    const scaleY = fabricObject.scaleY || 1;

    // 更新width属性（背景框宽度）- 与文字元素处理逻辑一致
    // 对于字幕元素，保持原始宽度，不应用缩放因子
    const width =
      fabricObject.width ||
      this.store.canvasWidth * CONSTANTS.CAPTION_STYLE.DEFAULT_WIDTH_RATIO;

    // 更新其他可能被修改的属性
    const fontSize = fabricObject.fontSize || this.globalCaptionStyle.fontSize;
    const fontFamily =
      fabricObject.fontFamily || this.globalCaptionStyle.fontFamily;

    // 同步其他可能通过控制框修改的属性
    const textAlign =
      (fabricObject.textAlign as "left" | "center" | "right") ||
      this.globalCaptionStyle.textAlign;
    const lineHeight =
      fabricObject.lineHeight || this.globalCaptionStyle.lineHeight;
    const charSpacing =
      fabricObject.charSpacing || this.globalCaptionStyle.charSpacing;

    // 同步颜色和样式属性
    const fontColor =
      this.extractColorFromFabricObject(fabricObject.fill) ||
      this.globalCaptionStyle.fontColor;
    const strokeWidth =
      fabricObject.strokeWidth || this.globalCaptionStyle.strokeWidth;
    const strokeColor =
      fabricObject.stroke || this.globalCaptionStyle.strokeColor;
    const backgroundColor =
      fabricObject.backgroundColor || this.globalCaptionStyle.backgroundColor;

    // 直接更新全局样式，避免触发字幕重新创建
    // 包含所有可能通过控制框修改的属性
    this.updateGlobalCaptionStyleOnly({
      scaleX: scaleX,
      scaleY: scaleY,
      width: width,
      fontSize: fontSize,
      fontFamily: fontFamily,
      textAlign: textAlign,
      lineHeight: lineHeight,
      charSpacing: charSpacing,
      fontColor: fontColor,
      strokeWidth: strokeWidth,
      strokeColor: strokeColor,
      backgroundColor: backgroundColor,
    });
  }

  /**
   * 从Fabric对象中提取颜色值
   * @param fill Fabric对象的fill属性
   * @returns 颜色字符串或undefined
   */
  private extractColorFromFabricObject(fill: any): string | undefined {
    if (typeof fill === "string") {
      return fill;
    }
    // 如果是渐变或图案，返回undefined，保持原有值
    return undefined;
  }

  /**
   * 计算字幕背景框宽度
   * @param text 字幕文本
   * @param style 字幕样式
   * @returns 背景框宽度
   */
  private calculateCaptionBackgroundWidth(text: string, style: any): number {
    // 使用全局样式中的width，如果没有则使用默认计算
    if (this.globalCaptionStyle.width) {
      return (
        this.globalCaptionStyle.width * (this.globalCaptionStyle.scaleX || 1)
      );
    }

    // 创建临时canvas来精确测量文本宽度
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    if (!ctx) {
      // 回退到估算方法
      const maxWidth =
        this.store.canvasWidth * CONSTANTS.CAPTION_STYLE.DEFAULT_WIDTH_RATIO;
      const fontSize = style.fontSize || 35;
      const charCount = text.length;
      const estimatedWidth = Math.min(charCount * fontSize * 0.6, maxWidth);
      return Math.max(estimatedWidth, 200);
    }

    const fontSize = style.fontSize || 35;
    const fontFamily = style.fontFamily || "Arial";
    const fontWeight = style.fontWeight || 700;

    ctx.font = `${fontWeight} ${fontSize}px ${fontFamily}`;

    // 计算每行的最大宽度
    const lines = text.split("\n");
    let maxLineWidth = 0;

    for (const line of lines) {
      const lineWidth = ctx.measureText(line).width;
      maxLineWidth = Math.max(maxLineWidth, lineWidth);
    }

    // 添加内边距和字符间距
    const charSpacing = style.charSpacing || 0;
    const padding = 40; // 左右内边距
    const totalWidth = maxLineWidth + text.length * charSpacing + padding;

    // 应用缩放
    const scaleX = this.globalCaptionStyle.scaleX || 1;
    return Math.max(totalWidth * scaleX, 200); // 最小宽度200px
  }

  /**
   * 计算字幕背景框高度
   * @param text 字幕文本
   * @param style 字幕样式
   * @returns 背景框高度
   */
  private calculateCaptionBackgroundHeight(text: string, style: any): number {
    const fontSize = style.fontSize || 35;
    const lineHeight = style.lineHeight || 1.2;
    const lineCount = text.split("\n").length;

    // 计算文本高度加上内边距
    const textHeight = fontSize * lineHeight * lineCount;
    const padding = 20; // 上下内边距

    // 应用缩放
    const scaleY = this.globalCaptionStyle.scaleY || 1;
    return (textHeight + padding) * scaleY;
  }

  /**
   * 调试方法：打印前端字幕位置信息
   */
  debugCaptionPosition(): void {
    if (!this.captionTextObject) return;

    const canvasWidth = this.store.canvasWidth;
    const canvasHeight = this.store.canvasHeight;
    const style = this.globalCaptionStyle;

    // 计算后端应该使用的坐标
    let backendX = canvasWidth / 2;
    if (style.originX === "left") {
      backendX = 0;
    } else if (style.originX === "right") {
      backendX = canvasWidth;
    }
    if (style.positionX !== undefined) {
      backendX += style.positionX;
    }

    // 后端Y坐标计算，与前端的captionTop计算逻辑保持一致
    // ASS的 \pos(x,y) 当配合 \an1/2/3 (底对齐) 时，y 是文本框的底部
    // 当配合 \an7/8/9 (顶对齐) 时，y 是文本框的顶部
    // 当配合 \an4/5/6 (中对齐) 时，y 是文本框的垂直中心
    let backendY: number;
    const defaultBottomMarginRatio = 0.02; // 与ASS_CONFIG.DEFAULT_MARGIN_RATIO一致

    switch (style.originY) {
      case "top":
        backendY = 0;
        break;
      case "center":
        backendY = canvasHeight / 2;
        break;
      case "bottom":
      default:
        backendY = canvasHeight - canvasHeight * defaultBottomMarginRatio;
        break;
    }
    if (style.positionY !== undefined) {
      backendY += style.positionY;
    }

    // 如果ASS使用顶部对齐(\an7/8/9)，则前端传入的top是正确的y
    // 如果ASS使用底部对齐(\an1/2/3)，则前端传入的top也是正确的y (因为fabric的originY也设为bottom)
    // 如果ASS使用中部对齐(\an4/5/6)，则前端传入的top也是正确的y (因为fabric的originY也设为center)
    // 因此，不再需要 baselineOffset 调整

    console.log("调试信息详细:");
    const baseBackendY = backendY - (style.positionY || 0); // Y before positionY adjustment
    console.log(
      `- 基础后端Y位置 (基于originY: ${style.originY}):`,
      baseBackendY
    );
    console.log("- positionY偏移:", style.positionY);
    console.log("- 最终后端Y坐标 (预期):", backendY);

    console.log("后端计算坐标 (预期):", { x: backendX, y: backendY });
    console.log("前端实际坐标:", {
      x: this.captionTextObject.left,
      y: this.captionTextObject.top,
    });

    const deltaX = Math.abs((this.captionTextObject.left || 0) - backendX);
    const deltaY = Math.abs((this.captionTextObject.top || 0) - backendY);

    console.log("坐标差异:", {
      deltaX: deltaX.toFixed(2),
      deltaY: deltaY.toFixed(2),
    });

    if (deltaY > 1 || deltaX > 1) {
      // 阈值可以设小一点，比如1像素
      console.log(
        "💡 坐标仍存在差异。请检查前端和后端的坐标计算逻辑是否完全一致，特别是 originX, originY, positionX, positionY 的处理。"
      );
      console.log(
        "   确保ASS的 \\an 标签与前端 originX/originY 的对应关系正确。"
      );
    } else {
      console.log("✅ 前后端坐标基本一致！");
    }

    console.log("========================");
  }

  /**
   * 应用字幕的高级样式（渐变、阴影）
   * 参考文本元素的样式应用逻辑
   */
  private _applyCaptionAdvancedStyles(
    textObject: fabric.TextboxWithPadding,
    captionStyles: CaptionStyle
  ) {
    // 应用渐变或纯色填充
    if (captionStyles.useGradient && captionStyles.gradientColors) {
      const gradient = new fabric.Gradient({
        type: "linear",
        coords: { x1: 0, y1: 0, x2: textObject.width || 200, y2: 0 },
        colorStops: [
          { offset: 0, color: captionStyles.gradientColors[0] },
          { offset: 1, color: captionStyles.gradientColors[1] },
        ],
      });
      textObject.set("fill", gradient);
    } else {
      textObject.set("fill", captionStyles.fontColor || "#ffffff");
    }

    // 应用阴影
    textObject.set(
      "shadow",
      new fabric.Shadow({
        color: captionStyles.shadowColor || "#000000",
        blur: captionStyles.shadowBlur || 0,
        offsetX: captionStyles.shadowOffsetX || 0,
        offsetY: captionStyles.shadowOffsetY || 0,
      })
    );
  }

  /**
   * 获取字幕的样式属性，使用全局样式
   * @returns 完整的样式属性对象
   */
  private getCaptionStyles() {
    // 根据画布高度自适应字体大小，但优先使用全局设置
    const adaptiveFontSize = Math.max(
      35,
      Math.round(this.store.canvasHeight * 0.035)
    );

    // 使用全局样式，但字体大小可以根据画布自适应
    const fontSize = this.globalCaptionStyle.fontSize || adaptiveFontSize;

    return {
      fontSize: fontSize,
      fontFamily: this.globalCaptionStyle.fontFamily,
      fontColor: this.globalCaptionStyle.fontColor,
      fontWeight: this.globalCaptionStyle.fontWeight,
      textAlign: this.globalCaptionStyle.textAlign,
      lineHeight: this.globalCaptionStyle.lineHeight,
      charSpacing: this.globalCaptionStyle.charSpacing,
      styles: this.globalCaptionStyle.styles,
      strokeColor: this.globalCaptionStyle.strokeColor,
      strokeWidth: this.globalCaptionStyle.strokeWidth,
      shadowColor: this.globalCaptionStyle.shadowColor,
      shadowBlur: this.globalCaptionStyle.shadowBlur,
      shadowOffsetX: this.globalCaptionStyle.shadowOffsetX,
      shadowOffsetY: this.globalCaptionStyle.shadowOffsetY,
      backgroundColor: this.globalCaptionStyle.backgroundColor,
      // 渐变属性
      useGradient: this.globalCaptionStyle.useGradient,
      gradientColors: this.globalCaptionStyle.gradientColors,
      // 位置信息
      positionX: this.globalCaptionStyle.positionX,
      positionY: this.globalCaptionStyle.positionY,
      originX: this.globalCaptionStyle.originX,
      originY: this.globalCaptionStyle.originY,
      // 变换属性
      scaleX: this.globalCaptionStyle.scaleX,
      scaleY: this.globalCaptionStyle.scaleY,
      width: this.globalCaptionStyle.width,
    };
  }

  /**
   * 获取按开始时间排序的第一个字幕
   * @returns 第一个字幕对象，如果没有字幕则返回null
   */
  getFirstCaption(): Caption | null {
    if (this.captions.length === 0) {
      return null;
    }

    // 按开始时间排序字幕，找到第一个字幕
    const sortedCaptions = [...this.captions].sort(
      (a, b) =>
        this.timeToSeconds(a.startTime) - this.timeToSeconds(b.startTime)
    );

    return sortedCaptions[0];
  }
}
